<!--
  Angular 动态路由最小功能实现
  =========================================

  这个 HTML 文件展示了如何集成和使用动态路由功能。

  核心概念：
  1. <router-outlet>: 这是 Angular 路由的核心指令。所有通过路由加载的组件都将在这里显示。
     动态路由服务 (DynamicRouteService) 会在应用启动时从后端获取路由配置，
     然后将这些路由注册到 Angular 的 Router 中。当用户导航到匹配的 URL 时，
     对应的组件就会被加载到 <router-outlet> 的位置。

  2. routerLink="/dashboard": 这是一个路由链接指令。它告诉 Angular Router，当用户点击这个链接时，
     应该导航到 'dashboard' 路径。这个路径是在 `assets/route-config.json` 中定义的，
     并在应用启动时由 DynamicRouteService 动态加载。

  工作流程：
  1. 应用启动 (main.ts)
  2. AppInitializerService (在 app.config.ts 中配置) 调用 DynamicRouteService.initializeRoutes()
  3. DynamicRouteService 调用 RouteConfigService 从 `assets/route-config.json` 加载配置
  4. DynamicRouteService 将 JSON 配置转换为 Angular Route 对象 (使用 route-mapping.ts)
  5. DynamicRouteService 使用 Router.resetConfig() 将新路由注册到应用中
  6. 用户点击下面的 "Go to Dashboard" 链接
  7. Angular Router 匹配到 'dashboard' 路径，并加载对应的 DashboardComponent
  8. DashboardComponent 的内容被渲染到下面的 <router-outlet> 中

  如何验证：
  - 启动应用后，你应该能看到 "Go to Dashboard" 链接。
  - 点击链接，URL 应该变为 /dashboard。
  - 页面上应该显示 "Dashboard" 标题和 "This component was loaded dynamically!" 的消息。
  - 打开浏览器的开发者工具，查看网络请求，你应该能看到 `route-config.json` 文件被加载。
  - 在控制台中，你应该能看到来自 DynamicRouteService 的日志，显示路由加载和注册的过程。
-->

<div style="padding: 20px">
  <h1>Angular 动态路由示例</h1>
  <p>这是一个最小化的实现，用于演示动态路由的核心功能。</p>

  <nav>
    <ul>
      <li>
        <!--
          这个链接指向一个动态加载的路由。
          'dashboard' 路由并不是在编译时定义的 (app.routes.ts 是空的),
          而是在运行时从 JSON 配置文件中加载的。
        -->
        <a routerLink="/dashboard">Go to Dashboard</a>
      </li>
    </ul>
  </nav>

  <hr />

  <!--
    路由出口
    所有动态加载的组件都将在这里显示。
  -->
  <router-outlet></router-outlet>
</div>
