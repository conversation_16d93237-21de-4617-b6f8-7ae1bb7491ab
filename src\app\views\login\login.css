.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: cover;
  background-position: center;
}

.login-box {
  width: 100%;
  max-width: 400px;
  padding: 20px;
}

.login-card {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.login-card .ant-card-head {
  text-align: center;
  border-bottom: none;
  padding: 24px 24px 0;
}

.login-card .ant-card-head-title {
  font-size: 24px;
  font-weight: 500;
  color: #333;
}

.login-card .ant-card-body {
  padding: 24px;
}

.login-form {
  max-width: 300px;
  margin: 0 auto;
}

.login-form-forgot {
  float: right;
  color: #667eea;
  text-decoration: none;
}

.login-form-forgot:hover {
  color: #764ba2;
}

.login-form-button {
  width: 100%;
  height: 40px;
  font-size: 16px;
  border-radius: 4px;
}

.login-form-button-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.login-form-button-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a3f8f 100%);
}

.login-form-button-primary[disabled] {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
}

.register-link {
  text-align: center;
  margin-top: 16px;
}

.register-link span {
  color: #666;
}

.register-link a {
  color: #667eea;
  text-decoration: none;
  margin-left: 4px;
}

.register-link a:hover {
  color: #764ba2;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-box {
    padding: 16px;
  }

  .login-card .ant-card-head-title {
    font-size: 20px;
  }

  .login-form {
    max-width: 100%;
  }
}

/* 加载状态 */
.login-form-button[nz-loading] {
  position: relative;
}

/* 输入框样式 */
.ant-input-affix-wrapper {
  border-radius: 4px;
}

.ant-input-affix-wrapper:hover {
  border-color: #667eea;
}

.ant-input-affix-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 复选框样式 */
.ant-checkbox-wrapper:hover .ant-checkbox-inner {
  border-color: #667eea;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #667eea;
  border-color: #667eea;
}